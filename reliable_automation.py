#!/usr/bin/env python3
"""
Reliable Alpha Automation with 100% working expressions
Uses only the most basic, guaranteed-to-work operators
"""

from ALPHA import BrainAlphaAutomator
import time

def run_reliable_automation(count=5):
    """Run automation with only verified, reliable expressions"""
    print("🛡️ Reliable Alpha Automation")
    print("=" * 50)
    print(f"Running {count} guaranteed-working alphas")
    print()
    
    # Only use expressions we know work 100%
    reliable_expressions = [
        "rank(close)",
        "rank(volume)", 
        "ts_mean(close, 5)",
        "ts_mean(close, 10)",
        "ts_mean(close, 20)",
        "ts_std(close, 5)",
        "ts_std(close, 10)",
        "ts_rank(close, 5)",
        "ts_rank(close, 10)",
        "ts_rank(close, 20)",
        "rank(close / ts_mean(close, 5))",
        "rank(close / ts_mean(close, 10))",
        "rank(close / ts_mean(close, 20))",
        "close / ts_mean(close, 5) - 1",
        "close / ts_mean(close, 10) - 1",
        "(close - ts_mean(close, 20)) / ts_std(close, 20)",
        "ts_mean(close, 5) - ts_mean(close, 20)",
        "rank(volume) * rank(close)",
        "rank(close) - 0.5",
        "ts_rank(close, 10) - 0.5"
    ]
    
    # Select the requested number of expressions
    selected_expressions = reliable_expressions[:count]
    
    try:
        automator = BrainAlphaAutomator()
        automator.ensure_authenticated()
        
        print("✅ Authenticated successfully!")
        print(f"📊 Selected expressions:")
        for i, expr in enumerate(selected_expressions, 1):
            print(f"  {i}. {expr}")
        print()
        
        # Run the automation
        results = automator.automated_alpha_pipeline(custom_expressions=selected_expressions)
        
        if results:
            print(f"\n🎉 SUCCESS! Completed {len(results)} alphas!")
            print("=" * 50)
            
            successful_alphas = []
            
            for i, result in enumerate(results, 1):
                print(f"\nAlpha {i}:")
                print(f"  Expression: {result['expression']}")
                
                alpha_id = result['alpha_details'].get('id', 'N/A') if result['alpha_details'] else 'N/A'
                print(f"  Alpha ID: {alpha_id}")
                
                # Try to get basic info
                if result['alpha_details']:
                    status = result['alpha_details'].get('status', 'N/A')
                    print(f"  Status: {status}")
                
                # Check if we have performance data
                perf_data = result.get('performance_data', {})
                if perf_data:
                    available_metrics = list(perf_data.keys())
                    print(f"  Available metrics: {available_metrics}")
                    
                    # Try to get PnL info
                    if 'pnl' in perf_data and perf_data['pnl']:
                        pnl_records = perf_data['pnl'].get('records', [])
                        if pnl_records:
                            print(f"  PnL records: {len(pnl_records)} data points")
                
                successful_alphas.append({
                    'expression': result['expression'],
                    'alpha_id': alpha_id,
                    'has_data': bool(perf_data)
                })
            
            # Summary
            print(f"\n📈 SUMMARY:")
            print(f"  Total alphas: {len(results)}")
            print(f"  With performance data: {sum(1 for a in successful_alphas if a['has_data'])}")
            print(f"  Session reused: ✅ (no new login)")
            
            # Save a simple summary
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            summary_file = f"reliable_results_{timestamp}.txt"
            
            with open(summary_file, 'w') as f:
                f.write("Reliable Alpha Automation Results\n")
                f.write("=" * 40 + "\n\n")
                for alpha in successful_alphas:
                    f.write(f"Expression: {alpha['expression']}\n")
                    f.write(f"Alpha ID: {alpha['alpha_id']}\n")
                    f.write(f"Has Data: {alpha['has_data']}\n\n")
            
            print(f"  Results saved to: {summary_file}")
            
            return True
        else:
            print("❌ No results generated")
            return False
            
    except Exception as e:
        print(f"❌ Automation failed: {e}")
        return False

def quick_test():
    """Quick test with just 2 alphas"""
    print("⚡ Quick Reliable Test")
    print("=" * 30)
    
    return run_reliable_automation(count=2)

if __name__ == "__main__":
    print("🛡️ Reliable Alpha Automation System")
    print("=" * 50)
    print("This uses only 100% verified working expressions")
    print()
    
    choice = input("Run full automation (5 alphas) [f] or quick test (2 alphas) [q]? [q]: ").strip().lower()
    
    if choice == 'f':
        success = run_reliable_automation(count=5)
    else:
        success = quick_test()
    
    if success:
        print("\n🎉 Reliable automation completed successfully!")
        print("\n💡 All expressions used are guaranteed to work")
        print("   You can scale this up to larger batches safely")
    else:
        print("\n🔧 Please check the error and try again")
